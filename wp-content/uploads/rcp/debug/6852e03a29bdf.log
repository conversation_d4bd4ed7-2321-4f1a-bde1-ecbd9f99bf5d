2025-6-17 20:22:52 - Starting rcp_check_for_expired_users() cron job.
2025-6-17 20:22:52 - No expired memberships found.
2025-6-17 20:22:52 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-17 20:22:52 - Processing expiration reminder. ID: 0; Period: -1month; Levels: 1, 3, 2.
2025-6-17 20:22:52 - <PERSON>minder is not enabled - exiting.
2025-6-18 10:09:24 - RCP Activate License Key called with License Key: e755a40f7edda5161d0fb0e5b547e8ea
2025-6-18 10:09:27 - Starting rcp_check_member_counts() cron job.
2025-6-18 10:09:27 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-18 10:50:18 - RCP upgraded from version 3.4.4 to 3.5.46.
2025-6-18 10:57:32 - Started new registration for membership level #10 via stripe.
2025-6-18 10:57:32 - Started new registration for membership level #10 via stripe.
2025-6-18 10:57:33 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-18 10:57:33',
  'has_trialed' => 0,
  'user_id' => 25833,
)
2025-6-18 10:57:33 - Created new customer #25771.
2025-6-18 10:57:33 - Registration type: new.
2025-6-18 10:57:33 - Adding new membership. Data: array (
  'customer_id' => '25771',
  'user_id' => '25833',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-18 10:57:33',
  'expiration_date' => '2025-07-18 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '214d0734cb7f6dcb55092c181f013c52',
)
2025-6-18 10:57:34 - New payment inserted. ID: 134064; User ID: 25833; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-18 10:57:34 - Registration for user #25833 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34478
2025-6-18 10:57:35 - Updating membership #34478. New data: array (
  'gateway_customer_id' => 'cus_SWRFE0WbiwM1FT',
).
2025-6-18 10:57:36 - Updating payment #134064 with new data: array (
  'status' => 'failed',
)
2025-6-18 10:57:36 - Disabling membership #34478.
2025-6-18 10:57:36 - Updating membership #34478. New data: array (
  'disabled' => 1,
).
2025-6-18 10:57:36 - "Can cancel" status for membership #34478: false. Reason: membership not active.
2025-6-18 10:57:36 - Stripe registration failed for user #25833. Error message: Please make sure that the name field doesn’t contain a card number.
2025-6-18 10:57:59 - Using recovered payment #134064 for registration. Transaction type: new.
2025-6-18 10:57:59 - Started new registration for membership level #10 via stripe.
2025-6-18 10:57:59 - Using recovered payment #134064 for registration. Transaction type: new.
2025-6-18 10:57:59 - Started new registration for membership level #10 via stripe.
2025-6-18 10:57:59 - Updating membership #34478. New data: array (
  'disabled' => 0,
).
2025-6-18 10:57:59 - Using recovered membership #34478 for registration.
2025-6-18 10:57:59 - Registration type: new.
2025-6-18 10:57:59 - Updating membership #34478. New data: array (
  'auto_renew' => true,
  'gateway' => 'stripe',
  'recurring_amount' => 2.99,
  'subscription_key' => 'd36629e56abe043e9bcbefb2ecb245c9',
  'status' => 'pending',
  'expiration_date' => '2025-07-18 23:59:59',
).
2025-6-18 10:57:59 - Using existing membership #34478 for payment.
2025-6-18 10:57:59 - Updating recovered payment #134064 with new data.
2025-6-18 10:57:59 - Updating payment #134064 with new data: array (
  'date' => '2025-06-18 10:57:59',
  'subscription' => 'Dynasty Trade Calculator (Monthly)',
  'object_id' => 10,
  'object_type' => 'subscription',
  'gateway' => 'stripe',
  'subscription_key' => 'd36629e56abe043e9bcbefb2ecb245c9',
  'amount' => 2.99,
  'user_id' => 25833,
  'customer_id' => '25771',
  'membership_id' => '34478',
  'status' => 'pending',
  'subtotal' => 2.99,
  'credits' => 0,
  'fees' => 0,
  'discount_amount' => 0,
  'discount_code' => '',
  'transaction_type' => 'new',
)
2025-6-18 10:58:00 - Registration for user #25833 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34478
2025-6-18 10:58:00 - Updating membership #34478. New data: array (
  'gateway_customer_id' => 'cus_SWRFE0WbiwM1FT',
).
2025-6-18 10:58:04 - Using recovered payment #134064 for registration. Transaction type: new.
2025-6-18 10:58:04 - Registration for user #25833 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34478
2025-6-18 10:58:06 - Updating payment #134064 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbOMlDrMO37mudt4XJ1ybZH',
  'status' => 'complete',
)
2025-6-18 10:58:06 - Completing registration for customer #25771 via payment #134064.
2025-6-18 10:58:06 - Activating membership #34478.
2025-6-18 10:58:06 - Disabling all memberships for customer #25771 except: '34478'.
2025-6-18 10:58:06 - Updating membership #34478. New data: array (
  'activated_date' => '2025-06-18 10:58:06',
).
2025-6-18 10:58:06 - Updating membership #34478. New data: array (
  'status' => 'active',
).
2025-6-18 10:58:06 - Removing old role subscriber, adding new role subscriber for membership #34478 (user ID #25833).
2025-6-18 10:58:06 - Active email sent to user #25833 for membership #34478.
2025-6-18 10:58:06 - Active email sent to admin(s) regarding membership #34478.
2025-6-18 10:58:06 - Payment Received email not sent to user #25833 for payment ID #134064 - message is empty or disabled.
2025-6-18 10:58:06 - Payment Received email not sent to admin(s) for payment ID #134064 - message is empty or disabled.
2025-6-18 10:58:06 - Updating membership #34478. New data: array (
  'times_billed' => 1,
).
2025-6-18 10:58:06 - Payment #134064 completed for member #25833 via Stripe gateway.
2025-6-18 10:58:08 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-18 23:59:59
2025-6-18 10:58:08 - Stripe Gateway: Creating subscription with 1752868688 start date via trial_end.
2025-6-18 10:58:10 - Updating membership #34478. New data: array (
  'gateway_subscription_id' => 'sub_1RbONJDrMO37mudtE61KPJFa',
).
2025-6-18 20:39:47 - Starting rcp_check_for_expired_users() cron job.
2025-6-18 20:39:47 - No expired memberships found.
2025-6-18 20:39:47 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-18 20:39:47 - Processing expiration reminder. ID: 0; Period: -1month; Levels: 1, 3, 2.
2025-6-18 20:39:47 - Reminder is not enabled - exiting.
2025-6-18 21:44:16 - Started new registration for membership level #10 via stripe.
2025-6-18 21:44:16 - Started new registration for membership level #10 via stripe.
2025-6-18 21:44:17 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-18 21:44:17',
  'has_trialed' => 0,
  'user_id' => 25834,
)
2025-6-18 21:44:17 - Created new customer #25772.
2025-6-18 21:44:17 - Registration type: new.
2025-6-18 21:44:17 - Adding new membership. Data: array (
  'customer_id' => '25772',
  'user_id' => '25834',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-18 21:44:17',
  'expiration_date' => '2025-07-18 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '8c9e726660c9b247ae90e249ae3d588d',
)
2025-6-18 21:44:18 - New payment inserted. ID: 134065; User ID: 25834; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-18 21:44:18 - Registration for user #25834 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34479
2025-6-18 21:44:19 - Updating membership #34479. New data: array (
  'gateway_customer_id' => 'cus_SWbfBSMeIxTTK1',
).
2025-6-18 21:44:22 - Using recovered payment #134065 for registration. Transaction type: new.
2025-6-18 21:44:22 - Registration for user #25834 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34479
2025-6-18 21:44:23 - Updating payment #134065 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbYSdDrMO37mudt0hgBGmeR',
  'status' => 'complete',
)
2025-6-18 21:44:23 - Completing registration for customer #25772 via payment #134065.
2025-6-18 21:44:23 - Activating membership #34479.
2025-6-18 21:44:23 - Disabling all memberships for customer #25772 except: '34479'.
2025-6-18 21:44:23 - Updating membership #34479. New data: array (
  'activated_date' => '2025-06-18 21:44:23',
).
2025-6-18 21:44:23 - Updating membership #34479. New data: array (
  'status' => 'active',
).
2025-6-18 21:44:24 - Active email sent to user #25834 for membership #34479.
2025-6-18 21:44:24 - Active email sent to admin(s) regarding membership #34479.
2025-6-18 21:44:24 - Payment Received email not sent to user #25834 for payment ID #134065 - message is empty or disabled.
2025-6-18 21:44:24 - Payment Received email not sent to admin(s) for payment ID #134065 - message is empty or disabled.
2025-6-18 21:44:24 - Updating membership #34479. New data: array (
  'times_billed' => 1,
).
2025-6-18 21:44:24 - Payment #134065 completed for member #25834 via Stripe gateway.
2025-6-18 23:04:59 - Starting rcp_check_member_counts() cron job.
2025-6-18 23:27:42 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-18 23:34:46 - Started new registration for membership level #10 via stripe.
2025-6-18 23:34:46 - Started new registration for membership level #10 via stripe.
2025-6-18 23:34:47 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-18 23:34:47',
  'has_trialed' => 0,
  'user_id' => 25835,
)
2025-6-18 23:34:47 - Created new customer #25773.
2025-6-18 23:34:47 - Registration type: new.
2025-6-18 23:34:47 - Adding new membership. Data: array (
  'customer_id' => '25773',
  'user_id' => '25835',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-18 23:34:47',
  'expiration_date' => '2025-07-18 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'a98a9c72151b4635b97f23f1221fc024',
)
2025-6-18 23:34:48 - New payment inserted. ID: 134066; User ID: 25835; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-18 23:34:48 - Registration for user #25835 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34480
2025-6-18 23:34:49 - Updating membership #34480. New data: array (
  'gateway_customer_id' => 'cus_SWdSKDXyuIHtoH',
).
2025-6-18 23:34:51 - Using recovered payment #134066 for registration. Transaction type: new.
2025-6-18 23:34:51 - Registration for user #25835 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34480
2025-6-18 23:34:52 - Updating payment #134066 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbaBZDrMO37mudt1iEivRta',
  'status' => 'complete',
)
2025-6-18 23:34:52 - Completing registration for customer #25773 via payment #134066.
2025-6-18 23:34:52 - Activating membership #34480.
2025-6-18 23:34:52 - Disabling all memberships for customer #25773 except: '34480'.
2025-6-18 23:34:52 - Updating membership #34480. New data: array (
  'activated_date' => '2025-06-18 23:34:52',
).
2025-6-18 23:34:52 - Updating membership #34480. New data: array (
  'status' => 'active',
).
2025-6-18 23:34:52 - Active email sent to user #25835 for membership #34480.
2025-6-18 23:34:52 - Active email sent to admin(s) regarding membership #34480.
2025-6-18 23:34:52 - Payment Received email not sent to user #25835 for payment ID #134066 - message is empty or disabled.
2025-6-18 23:34:52 - Payment Received email not sent to admin(s) for payment ID #134066 - message is empty or disabled.
2025-6-18 23:34:52 - Updating membership #34480. New data: array (
  'times_billed' => 1,
).
2025-6-18 23:34:52 - Payment #134066 completed for member #25835 via Stripe gateway.
2025-6-19 00:00:34 - Started new registration for membership level #10 via stripe.
2025-6-19 00:00:35 - Started new registration for membership level #10 via stripe.
2025-6-19 00:00:35 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 00:00:35',
  'has_trialed' => 0,
  'user_id' => 25836,
)
2025-6-19 00:00:35 - Created new customer #25774.
2025-6-19 00:00:35 - Registration type: new.
2025-6-19 00:00:35 - Adding new membership. Data: array (
  'customer_id' => '25774',
  'user_id' => '25836',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 00:00:35',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '49150c59f1f68d1c8e7aaf134fb19fa1',
)
2025-6-19 00:00:36 - New payment inserted. ID: 134067; User ID: 25836; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 00:00:36 - Registration for user #25836 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34481
2025-6-19 00:00:37 - Updating membership #34481. New data: array (
  'gateway_customer_id' => 'cus_SWds7cGB4p7R6l',
).
2025-6-19 00:00:40 - Using recovered payment #134067 for registration. Transaction type: new.
2025-6-19 00:00:40 - Registration for user #25836 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34481
2025-6-19 00:00:41 - Updating payment #134067 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbaaXDrMO37mudt4cpjanyf',
  'status' => 'complete',
)
2025-6-19 00:00:41 - Completing registration for customer #25774 via payment #134067.
2025-6-19 00:00:41 - Activating membership #34481.
2025-6-19 00:00:41 - Disabling all memberships for customer #25774 except: '34481'.
2025-6-19 00:00:41 - Updating membership #34481. New data: array (
  'activated_date' => '2025-06-19 00:00:41',
).
2025-6-19 00:00:41 - Updating membership #34481. New data: array (
  'status' => 'active',
).
2025-6-19 00:00:41 - Active email sent to user #25836 for membership #34481.
2025-6-19 00:00:41 - Active email sent to admin(s) regarding membership #34481.
2025-6-19 00:00:41 - Payment Received email not sent to user #25836 for payment ID #134067 - message is empty or disabled.
2025-6-19 00:00:41 - Payment Received email not sent to admin(s) for payment ID #134067 - message is empty or disabled.
2025-6-19 00:00:41 - Updating membership #34481. New data: array (
  'times_billed' => 1,
).
2025-6-19 00:00:41 - Payment #134067 completed for member #25836 via Stripe gateway.
2025-6-19 00:06:31 - Started new registration for membership level #10 via stripe.
2025-6-19 00:07:02 - Started new registration for membership level #10 via stripe.
2025-6-19 00:07:02 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 00:07:02',
  'has_trialed' => 0,
  'user_id' => 25837,
)
2025-6-19 00:07:02 - Created new customer #25775.
2025-6-19 00:07:02 - Registration type: new.
2025-6-19 00:07:02 - Adding new membership. Data: array (
  'customer_id' => '25775',
  'user_id' => '25837',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 00:07:02',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '9fa2def6a62901a11021e2552c6a3473',
)
2025-6-19 00:07:03 - New payment inserted. ID: 134068; User ID: 25837; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 00:07:03 - Registration for user #25837 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34482
2025-6-19 00:07:04 - Updating membership #34482. New data: array (
  'gateway_customer_id' => 'cus_SWdyuJnOnFeqEn',
).
2025-6-19 00:13:00 - Started new registration for membership level #10 via stripe.
2025-6-19 00:13:00 - Started new registration for membership level #10 via stripe.
2025-6-19 00:13:01 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 00:13:01',
  'has_trialed' => 0,
  'user_id' => 25838,
)
2025-6-19 00:13:01 - Created new customer #25776.
2025-6-19 00:13:01 - Registration type: new.
2025-6-19 00:13:01 - Adding new membership. Data: array (
  'customer_id' => '25776',
  'user_id' => '25838',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 00:13:01',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '358dfb25a4429df748ee6b340db23b36',
)
2025-6-19 00:13:02 - New payment inserted. ID: 134069; User ID: 25838; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 00:13:02 - Registration for user #25838 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34483
2025-6-19 00:13:03 - Updating membership #34483. New data: array (
  'gateway_customer_id' => 'cus_SWe4WZKt3l8hLo',
).
2025-6-19 00:13:05 - Using recovered payment #134069 for registration. Transaction type: new.
2025-6-19 00:13:05 - Registration for user #25838 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34483
2025-6-19 00:13:06 - Updating payment #134069 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbamZDrMO37mudt2x9jpEsW',
  'status' => 'complete',
)
2025-6-19 00:13:06 - Completing registration for customer #25776 via payment #134069.
2025-6-19 00:13:06 - Activating membership #34483.
2025-6-19 00:13:06 - Disabling all memberships for customer #25776 except: '34483'.
2025-6-19 00:13:06 - Updating membership #34483. New data: array (
  'activated_date' => '2025-06-19 00:13:06',
).
2025-6-19 00:13:06 - Updating membership #34483. New data: array (
  'status' => 'active',
).
2025-6-19 00:13:07 - Active email sent to user #25838 for membership #34483.
2025-6-19 00:13:07 - Active email sent to admin(s) regarding membership #34483.
2025-6-19 00:13:07 - Payment Received email not sent to user #25838 for payment ID #134069 - message is empty or disabled.
2025-6-19 00:13:07 - Payment Received email not sent to admin(s) for payment ID #134069 - message is empty or disabled.
2025-6-19 00:13:07 - Updating membership #34483. New data: array (
  'times_billed' => 1,
).
2025-6-19 00:13:07 - Payment #134069 completed for member #25838 via Stripe gateway.
2025-6-19 00:44:34 - Started new registration for membership level #10 via stripe.
2025-6-19 00:44:34 - Started new registration for membership level #10 via stripe.
2025-6-19 00:44:34 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 00:44:34',
  'has_trialed' => 0,
  'user_id' => 25839,
)
2025-6-19 00:44:34 - Created new customer #25777.
2025-6-19 00:44:34 - Registration type: new.
2025-6-19 00:44:34 - Adding new membership. Data: array (
  'customer_id' => '25777',
  'user_id' => '25839',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 00:44:34',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '********************************',
)
2025-6-19 00:44:35 - New payment inserted. ID: 134070; User ID: 25839; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 00:44:36 - Registration for user #25839 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34484
2025-6-19 00:44:36 - Updating membership #34484. New data: array (
  'gateway_customer_id' => 'cus_SWeaUHrU4bHWZL',
).
2025-6-19 00:44:39 - Using recovered payment #134070 for registration. Transaction type: new.
2025-6-19 00:44:39 - Registration for user #25839 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34484
2025-6-19 00:44:40 - Updating payment #134070 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbbH6DrMO37mudt1KABu6iH',
  'status' => 'complete',
)
2025-6-19 00:44:40 - Completing registration for customer #25777 via payment #134070.
2025-6-19 00:44:40 - Activating membership #34484.
2025-6-19 00:44:40 - Disabling all memberships for customer #25777 except: '34484'.
2025-6-19 00:44:40 - Updating membership #34484. New data: array (
  'activated_date' => '2025-06-19 00:44:40',
).
2025-6-19 00:44:40 - Updating membership #34484. New data: array (
  'status' => 'active',
).
2025-6-19 00:44:40 - Active email sent to user #25839 for membership #34484.
2025-6-19 00:44:40 - Active email sent to admin(s) regarding membership #34484.
2025-6-19 00:44:40 - Payment Received email not sent to user #25839 for payment ID #134070 - message is empty or disabled.
2025-6-19 00:44:40 - Payment Received email not sent to admin(s) for payment ID #134070 - message is empty or disabled.
2025-6-19 00:44:40 - Updating membership #34484. New data: array (
  'times_billed' => 1,
).
2025-6-19 00:44:40 - Payment #134070 completed for member #25839 via Stripe gateway.
2025-6-19 01:21:02 - Started new registration for membership level #10 via stripe.
2025-6-19 01:21:03 - Started new registration for membership level #10 via stripe.
2025-6-19 01:21:03 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 01:21:03',
  'has_trialed' => 0,
  'user_id' => 25840,
)
2025-6-19 01:21:03 - Created new customer #25778.
2025-6-19 01:21:03 - Registration type: new.
2025-6-19 01:21:03 - Adding new membership. Data: array (
  'customer_id' => '25778',
  'user_id' => '25840',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 01:21:03',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '851696bc678e0a9f402ecce4e5aea95c',
)
2025-6-19 01:21:04 - New payment inserted. ID: 134071; User ID: 25840; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 01:21:04 - Registration for user #25840 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34485
2025-6-19 01:21:04 - Updating membership #34485. New data: array (
  'gateway_customer_id' => 'cus_SWfAoekEP7EeG9',
).
2025-6-19 01:21:07 - Using recovered payment #134071 for registration. Transaction type: new.
2025-6-19 01:21:07 - Registration for user #25840 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34485
2025-6-19 01:21:08 - Updating payment #134071 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbbqPDrMO37mudt4W5S5nYN',
  'status' => 'complete',
)
2025-6-19 01:21:08 - Completing registration for customer #25778 via payment #134071.
2025-6-19 01:21:08 - Activating membership #34485.
2025-6-19 01:21:08 - Disabling all memberships for customer #25778 except: '34485'.
2025-6-19 01:21:08 - Updating membership #34485. New data: array (
  'activated_date' => '2025-06-19 01:21:08',
).
2025-6-19 01:21:08 - Updating membership #34485. New data: array (
  'status' => 'active',
).
2025-6-19 01:21:08 - Active email sent to user #25840 for membership #34485.
2025-6-19 01:21:08 - Active email sent to admin(s) regarding membership #34485.
2025-6-19 01:21:08 - Payment Received email not sent to user #25840 for payment ID #134071 - message is empty or disabled.
2025-6-19 01:21:08 - Payment Received email not sent to admin(s) for payment ID #134071 - message is empty or disabled.
2025-6-19 01:21:08 - Updating membership #34485. New data: array (
  'times_billed' => 1,
).
2025-6-19 01:21:08 - Payment #134071 completed for member #25840 via Stripe gateway.
2025-6-19 01:21:10 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-19 23:59:59
2025-6-19 01:21:10 - Stripe Gateway: Creating subscription with 1752920470 start date via trial_end.
2025-6-19 01:21:11 - Updating membership #34485. New data: array (
  'gateway_subscription_id' => 'sub_1RbbqUDrMO37mudtjosscDSF',
).
2025-6-19 01:26:30 - Started new registration for membership level #11 via stripe.
2025-6-19 01:26:30 - Registration cancelled with the following errors: The discount you entered is invalid.
2025-6-19 01:26:51 - Started new registration for membership level #10 via stripe.
2025-6-19 01:26:52 - Started new registration for membership level #10 via stripe.
2025-6-19 01:26:52 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 01:26:52',
  'has_trialed' => 0,
  'user_id' => 25841,
)
2025-6-19 01:26:52 - Created new customer #25779.
2025-6-19 01:26:52 - Registration type: new.
2025-6-19 01:26:52 - Adding new membership. Data: array (
  'customer_id' => '25779',
  'user_id' => '25841',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 01:26:52',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'fb0ed3d70014170b4644b89e9d5ef798',
)
2025-6-19 01:26:53 - New payment inserted. ID: 134072; User ID: 25841; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 01:26:53 - Registration for user #25841 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34486
2025-6-19 01:26:54 - Updating membership #34486. New data: array (
  'gateway_customer_id' => 'cus_SWfGsZ0r5AEsbX',
).
2025-6-19 01:26:56 - Using recovered payment #134072 for registration. Transaction type: new.
2025-6-19 01:26:57 - Registration for user #25841 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34486
2025-6-19 01:26:58 - Updating payment #134072 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rbbw2DrMO37mudt1H9qSrgW',
  'status' => 'complete',
)
2025-6-19 01:26:58 - Completing registration for customer #25779 via payment #134072.
2025-6-19 01:26:58 - Activating membership #34486.
2025-6-19 01:26:58 - Disabling all memberships for customer #25779 except: '34486'.
2025-6-19 01:26:58 - Updating membership #34486. New data: array (
  'activated_date' => '2025-06-19 01:26:58',
).
2025-6-19 01:26:58 - Updating membership #34486. New data: array (
  'status' => 'active',
).
2025-6-19 01:26:58 - Active email sent to user #25841 for membership #34486.
2025-6-19 01:26:58 - Active email sent to admin(s) regarding membership #34486.
2025-6-19 01:26:58 - Payment Received email not sent to user #25841 for payment ID #134072 - message is empty or disabled.
2025-6-19 01:26:58 - Payment Received email not sent to admin(s) for payment ID #134072 - message is empty or disabled.
2025-6-19 01:26:58 - Updating membership #34486. New data: array (
  'times_billed' => 1,
).
2025-6-19 01:26:58 - Payment #134072 completed for member #25841 via Stripe gateway.
2025-6-19 09:42:54 - Started new registration for membership level #10 via stripe.
2025-6-19 09:42:55 - Started new registration for membership level #10 via stripe.
2025-6-19 09:42:55 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 09:42:55',
  'has_trialed' => 0,
  'user_id' => 25842,
)
2025-6-19 09:42:55 - Created new customer #25780.
2025-6-19 09:42:55 - Registration type: new.
2025-6-19 09:42:55 - Adding new membership. Data: array (
  'customer_id' => '25780',
  'user_id' => '25842',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 09:42:55',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'f3d66f54c8de04a2516094b1ef3c8812',
)
2025-6-19 09:42:56 - New payment inserted. ID: 134073; User ID: 25842; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 09:42:56 - Registration for user #25842 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34487
2025-6-19 09:42:57 - Updating membership #34487. New data: array (
  'gateway_customer_id' => 'cus_SWnGZjOVQJySdC',
).
2025-6-19 09:43:00 - Using recovered payment #134073 for registration. Transaction type: new.
2025-6-19 09:43:00 - Registration for user #25842 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34487
2025-6-19 09:43:01 - Updating payment #134073 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rbjg5DrMO37mudt14nmoAoX',
  'status' => 'complete',
)
2025-6-19 09:43:01 - Completing registration for customer #25780 via payment #134073.
2025-6-19 09:43:01 - Activating membership #34487.
2025-6-19 09:43:01 - Disabling all memberships for customer #25780 except: '34487'.
2025-6-19 09:43:01 - Updating membership #34487. New data: array (
  'activated_date' => '2025-06-19 09:43:01',
).
2025-6-19 09:43:01 - Updating membership #34487. New data: array (
  'status' => 'active',
).
2025-6-19 09:43:01 - Active email sent to user #25842 for membership #34487.
2025-6-19 09:43:01 - Active email sent to admin(s) regarding membership #34487.
2025-6-19 09:43:01 - Payment Received email not sent to user #25842 for payment ID #134073 - message is empty or disabled.
2025-6-19 09:43:01 - Payment Received email not sent to admin(s) for payment ID #134073 - message is empty or disabled.
2025-6-19 09:43:01 - Updating membership #34487. New data: array (
  'times_billed' => 1,
).
2025-6-19 09:43:01 - Payment #134073 completed for member #25842 via Stripe gateway.
2025-6-19 10:42:50 - Started new registration for membership level #10 via stripe.
2025-6-19 10:42:51 - Started new registration for membership level #10 via stripe.
2025-6-19 10:42:51 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 10:42:51',
  'has_trialed' => 0,
  'user_id' => 25843,
)
2025-6-19 10:42:51 - Created new customer #25781.
2025-6-19 10:42:51 - Registration type: new.
2025-6-19 10:42:51 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 10:42:51',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'b80434b4113fe4ac7ad478321c4e52ea',
)
2025-6-19 10:42:51 - New payment inserted. ID: 134074; User ID: 25843; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 10:42:52 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34488
2025-6-19 10:42:52 - Updating membership #34488. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 10:42:55 - Using recovered payment #134074 for registration. Transaction type: new.
2025-6-19 10:42:55 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34488
2025-6-19 10:42:57 - Updating payment #134074 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rbkc5DrMO37mudt3jlkFCH1',
  'status' => 'complete',
)
2025-6-19 10:42:57 - Completing registration for customer #25781 via payment #134074.
2025-6-19 10:42:57 - Activating membership #34488.
2025-6-19 10:42:57 - Disabling all memberships for customer #25781 except: '34488'.
2025-6-19 10:42:57 - Updating membership #34488. New data: array (
  'activated_date' => '2025-06-19 10:42:57',
).
2025-6-19 10:42:57 - Updating membership #34488. New data: array (
  'status' => 'active',
).
2025-6-19 10:42:57 - Active email sent to user #25843 for membership #34488.
2025-6-19 10:42:57 - Active email sent to admin(s) regarding membership #34488.
2025-6-19 10:42:57 - Payment Received email not sent to user #25843 for payment ID #134074 - message is empty or disabled.
2025-6-19 10:42:57 - Payment Received email not sent to admin(s) for payment ID #134074 - message is empty or disabled.
2025-6-19 10:42:57 - Updating membership #34488. New data: array (
  'times_billed' => 1,
).
2025-6-19 10:42:57 - Payment #134074 completed for member #25843 via Stripe gateway.
2025-6-19 10:43:19 - "Can cancel" status for membership #34488: false. Reason: membership not recurring.
2025-6-19 10:43:19 - "Can cancel" status for membership #34488: false. Reason: membership not recurring.
2025-6-19 10:43:27 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:43:28 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:43:28 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:43:28 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:43:28 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:43:46 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:43:46 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:43:46 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:43:46 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:44:12 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:44:12 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:44:12 - Started new registration for membership level #11 via stripe.
2025-6-19 10:44:12 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:44:12 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:44:12 - Started new registration for membership level #11 via stripe.
2025-6-19 10:44:12 - Registration type: downgrade.
2025-6-19 10:44:12 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 27.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-19 10:44:12',
  'expiration_date' => '2026-06-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'b284ba4c2110589a9a82f14b5d234753',
  'upgraded_from' => '34488',
)
2025-6-19 10:44:12 - New payment inserted. ID: 134075; User ID: 25843; Amount: 27.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-19 10:44:13 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34489
2025-6-19 10:44:13 - Updating membership #34489. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 10:44:16 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:44:16 - Using recovered payment #134075 for registration. Transaction type: downgrade.
2025-6-19 10:44:16 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:44:16 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34489
2025-6-19 10:44:18 - Updating payment #134075 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbkdNDrMO37mudt39sXC1u8',
  'status' => 'complete',
)
2025-6-19 10:44:18 - Completing registration for customer #25781 via payment #134075.
2025-6-19 10:44:18 - Activating membership #34489.
2025-6-19 10:44:18 - Disabling all memberships for customer #25781 except: '34489'.
2025-6-19 10:44:18 - Disabling membership #34488.
2025-6-19 10:44:18 - Updating membership #34488. New data: array (
  'disabled' => 1,
).
2025-6-19 10:44:18 - "Can cancel" status for membership #34488: false. Reason: membership not recurring.
2025-6-19 10:44:18 - Updating membership #34489. New data: array (
  'activated_date' => '2025-06-19 10:44:18',
).
2025-6-19 10:44:18 - Updating membership #34489. New data: array (
  'status' => 'active',
).
2025-6-19 10:44:18 - Removing old role subscriber, adding new role subscriber for membership #34489 (user ID #25843).
2025-6-19 10:44:18 - Active email sent to user #25843 for membership #34489.
2025-6-19 10:44:18 - Active email sent to admin(s) regarding membership #34489.
2025-6-19 10:44:18 - Payment Received email not sent to user #25843 for payment ID #134075 - message is empty or disabled.
2025-6-19 10:44:18 - Payment Received email not sent to admin(s) for payment ID #134075 - message is empty or disabled.
2025-6-19 10:44:18 - Updating membership #34489. New data: array (
  'times_billed' => 1,
).
2025-6-19 10:44:18 - Payment #134075 completed for member #25843 via Stripe gateway.
2025-6-19 10:44:20 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-19 23:59:59
2025-6-19 10:44:20 - Stripe Gateway: Creating subscription with 1781898260 start date via trial_end.
2025-6-19 10:44:22 - Updating membership #34489. New data: array (
  'gateway_subscription_id' => 'sub_1RbkdVDrMO37mudtIu7RLugd',
).
2025-6-19 10:53:33 - "Can cancel" status for membership #34489: true.
2025-6-19 10:53:33 - "Can cancel" status for membership #34489: true.
2025-6-19 10:53:41 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:53:43 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:53:43 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:53:43 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:53:43 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:53:48 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 10:53:48 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:53:48 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 10:53:48 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:54:00 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 10:54:00 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:54:01 - Started new registration for membership level #10 via stripe.
2025-6-19 10:54:01 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 10:54:01 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:54:01 - Started new registration for membership level #10 via stripe.
2025-6-19 10:54:01 - Registration type: upgrade.
2025-6-19 10:54:01 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 10:54:01',
  'expiration_date' => '2026-05-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '90d559517ee6c6d59adf67f0ba7a00e6',
  'upgraded_from' => '34489',
)
2025-6-19 10:54:01 - New payment inserted. ID: 134076; User ID: 25843; Amount: 0.00; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 10:54:02 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2026-05-19 10:54:02; Membership ID: 34490
2025-6-19 10:54:02 - Updating membership #34490. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 10:54:04 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 10:54:04 - Using recovered payment #134076 for registration. Transaction type: upgrade.
2025-6-19 10:54:04 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:54:04 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2026-05-19 10:54:04; Membership ID: 34490
2025-6-19 10:54:05 - Updating payment #134076 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-19 10:54:06 - Completing registration for customer #25781 via payment #134076.
2025-6-19 10:54:06 - Activating membership #34490.
2025-6-19 10:54:06 - Disabling all memberships for customer #25781 except: '34490'.
2025-6-19 10:54:06 - Disabling membership #34489.
2025-6-19 10:54:06 - Updating membership #34489. New data: array (
  'disabled' => 1,
).
2025-6-19 10:54:06 - "Can cancel" status for membership #34489: true.
2025-6-19 10:54:06 - "Can cancel" status for membership #34489: true.
2025-6-19 10:54:07 - Failed to cancel Stripe payment profile sub_1RbkdVDrMO37mudtIu7RLugd. Error code: resource_missing; Error Message: No such subscription: 'sub_1RbkdVDrMO37mudtIu7RLugd'.
2025-6-19 10:54:07 - Updating membership #34489. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-19 10:54:07',
).
2025-6-19 10:54:07 - Updating recurring status for membership #34489. Customer ID: 25781; Previous: true; New: false
2025-6-19 10:54:07 - Updating membership #34489. New data: array (
  'auto_renew' => 0,
).
2025-6-19 10:54:07 - Payment profile successfully cancelled for membership #34489.
2025-6-19 10:54:07 - Updating membership #34490. New data: array (
  'activated_date' => '2025-06-19 10:54:07',
).
2025-6-19 10:54:07 - Updating membership #34490. New data: array (
  'status' => 'active',
).
2025-6-19 10:54:07 - Removing old role subscriber, adding new role subscriber for membership #34490 (user ID #25843).
2025-6-19 10:54:07 - Active email sent to user #25843 for membership #34490.
2025-6-19 10:54:07 - Active email sent to admin(s) regarding membership #34490.
2025-6-19 10:54:07 - Payment Received email not sent to user #25843 - payment amount is 0.
2025-6-19 10:54:07 - Updating membership #34490. New data: array (
  'times_billed' => 1,
).
2025-6-19 10:54:07 - Payment #134076 completed for member #25843 via Stripe gateway.
2025-6-19 10:54:09 - Stripe Gateway: Using subscription start date for subscription: 2026-05-19 10:54:04
2025-6-19 10:54:09 - Stripe Gateway: Creating subscription with 1779188044 start date via trial_end.
2025-6-19 10:54:10 - Updating membership #34490. New data: array (
  'gateway_subscription_id' => 'sub_1RbkmzDrMO37mudt2hbAVbBV',
).
2025-6-19 10:54:10 - Updating payment #134076 with new data: array (
  'transaction_id' => 'sub_1RbkmzDrMO37mudt2hbAVbBV',
)
2025-6-19 10:59:30 - "Can cancel" status for membership #34490: true.
2025-6-19 10:59:30 - "Can cancel" status for membership #34490: true.
2025-6-19 10:59:37 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:38 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:59:38 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:38 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:59:38 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:45 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:59:45 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:45 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:59:45 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:53 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:59:53 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:53 - Started new registration for membership level #11 via stripe.
2025-6-19 10:59:54 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:59:54 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:54 - Started new registration for membership level #11 via stripe.
2025-6-19 10:59:54 - Registration type: downgrade.
2025-6-19 10:59:54 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 27.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-19 10:59:54',
  'expiration_date' => '2026-06-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '339795105304603a2ef9e8b7438de39a',
  'upgraded_from' => '34490',
)
2025-6-19 10:59:55 - New payment inserted. ID: 134077; User ID: 25843; Amount: 27.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-19 10:59:56 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34491
2025-6-19 10:59:56 - Updating membership #34491. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 10:59:59 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:59:59 - Using recovered payment #134077 for registration. Transaction type: downgrade.
2025-6-19 10:59:59 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:59 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34491
2025-6-19 11:00:01 - Updating payment #134077 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbksbDrMO37mudt29mTTkav',
  'status' => 'complete',
)
2025-6-19 11:00:01 - Completing registration for customer #25781 via payment #134077.
2025-6-19 11:00:01 - Activating membership #34491.
2025-6-19 11:00:01 - Disabling all memberships for customer #25781 except: '34491'.
2025-6-19 11:00:01 - Disabling membership #34490.
2025-6-19 11:00:01 - Updating membership #34490. New data: array (
  'disabled' => 1,
).
2025-6-19 11:00:01 - "Can cancel" status for membership #34490: true.
2025-6-19 11:00:01 - "Can cancel" status for membership #34490: true.
2025-6-19 11:00:03 - Failed to cancel Stripe payment profile sub_1RbkmzDrMO37mudt2hbAVbBV. Error code: resource_missing; Error Message: No such subscription: 'sub_1RbkmzDrMO37mudt2hbAVbBV'.
2025-6-19 11:00:03 - Updating membership #34490. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-19 11:00:03',
).
2025-6-19 11:00:03 - Updating recurring status for membership #34490. Customer ID: 25781; Previous: true; New: false
2025-6-19 11:00:03 - Updating membership #34490. New data: array (
  'auto_renew' => 0,
).
2025-6-19 11:00:03 - Payment profile successfully cancelled for membership #34490.
2025-6-19 11:00:03 - Updating membership #34491. New data: array (
  'activated_date' => '2025-06-19 11:00:03',
).
2025-6-19 11:00:03 - Updating membership #34491. New data: array (
  'status' => 'active',
).
2025-6-19 11:00:03 - Removing old role subscriber, adding new role subscriber for membership #34491 (user ID #25843).
2025-6-19 11:00:03 - Active email sent to user #25843 for membership #34491.
2025-6-19 11:00:03 - Active email sent to admin(s) regarding membership #34491.
2025-6-19 11:00:03 - Payment Received email not sent to user #25843 for payment ID #134077 - message is empty or disabled.
2025-6-19 11:00:03 - Payment Received email not sent to admin(s) for payment ID #134077 - message is empty or disabled.
2025-6-19 11:00:03 - Updating membership #34491. New data: array (
  'times_billed' => 1,
).
2025-6-19 11:00:03 - Payment #134077 completed for member #25843 via Stripe gateway.
2025-6-19 11:00:05 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-19 23:59:59
2025-6-19 11:00:05 - Stripe Gateway: Creating subscription with 1781899205 start date via trial_end.
2025-6-19 11:00:06 - Updating membership #34491. New data: array (
  'gateway_subscription_id' => 'sub_1RbksjDrMO37mudtDlsb3Xz5',
).
2025-6-19 14:31:39 - Starting rcp_check_for_expired_users() cron job.
2025-6-19 14:31:39 - No expired memberships found.
2025-6-19 14:31:39 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-19 14:31:39 - Processing expiration reminder. ID: 0; Period: -1month; Levels: 1, 3, 2.
2025-6-19 14:31:39 - Reminder is not enabled - exiting.
2025-6-19 14:41:18 - "Can cancel" status for membership #34458: false. Reason: membership not recurring.
2025-6-19 14:41:18 - "Can cancel" status for membership #34458: false. Reason: membership not recurring.
2025-6-19 14:41:29 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:41:29 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:41:29 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:41:29 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:18 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:42:18 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:18 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:42:18 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:35 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:42:35 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:35 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:42:36 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:43 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:42:43 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:43 - Started new registration for membership level #10 via stripe.
2025-6-19 14:42:43 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:42:43 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:43 - Started new registration for membership level #10 via stripe.
2025-6-19 14:42:43 - Registration type: upgrade.
2025-6-19 14:42:43 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 14:42:43',
  'expiration_date' => '2026-05-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '973c6717f41fb6e9fa297ae15ecb8e19',
  'upgraded_from' => '34491',
)
2025-6-19 14:42:45 - New payment inserted. ID: 134078; User ID: 25843; Amount: 0.00; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 14:42:45 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2026-05-19 14:42:45; Membership ID: 34492
2025-6-19 14:42:45 - Updating membership #34492. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 14:42:47 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:42:47 - Using recovered payment #134078 for registration. Transaction type: upgrade.
2025-6-19 14:42:47 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:47 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2026-05-19 14:42:47; Membership ID: 34492
2025-6-19 14:42:49 - Updating payment #134078 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-19 14:42:49 - Completing registration for customer #25781 via payment #134078.
2025-6-19 14:42:49 - Activating membership #34492.
2025-6-19 14:42:49 - Disabling all memberships for customer #25781 except: '34492'.
2025-6-19 14:42:49 - Disabling membership #34491.
2025-6-19 14:42:49 - Updating membership #34491. New data: array (
  'disabled' => 1,
).
2025-6-19 14:42:49 - "Can cancel" status for membership #34491: true.
2025-6-19 14:42:49 - "Can cancel" status for membership #34491: true.
2025-6-19 14:42:50 - Failed to cancel Stripe payment profile sub_1RbksjDrMO37mudtDlsb3Xz5. Error code: resource_missing; Error Message: No such subscription: 'sub_1RbksjDrMO37mudtDlsb3Xz5'.
2025-6-19 14:42:50 - Updating membership #34491. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-19 14:42:50',
).
2025-6-19 14:42:50 - Updating recurring status for membership #34491. Customer ID: 25781; Previous: true; New: false
2025-6-19 14:42:50 - Updating membership #34491. New data: array (
  'auto_renew' => 0,
).
2025-6-19 14:42:50 - Payment profile successfully cancelled for membership #34491.
2025-6-19 14:42:50 - Updating membership #34492. New data: array (
  'activated_date' => '2025-06-19 14:42:50',
).
2025-6-19 14:42:50 - Updating membership #34492. New data: array (
  'status' => 'active',
).
2025-6-19 14:42:50 - Removing old role subscriber, adding new role subscriber for membership #34492 (user ID #25843).
2025-6-19 14:42:50 - Active email sent to user #25843 for membership #34492.
2025-6-19 14:42:50 - Active email sent to admin(s) regarding membership #34492.
2025-6-19 14:42:50 - Payment Received email not sent to user #25843 - payment amount is 0.
2025-6-19 14:42:50 - Updating membership #34492. New data: array (
  'times_billed' => 1,
).
2025-6-19 14:42:50 - Payment #134078 completed for member #25843 via Stripe gateway.
2025-6-19 14:42:52 - Stripe Gateway: Using subscription start date for subscription: 2026-05-19 14:42:47
2025-6-19 14:42:52 - Stripe Gateway: Creating subscription with 1779201767 start date via trial_end.
2025-6-19 14:42:53 - Updating membership #34492. New data: array (
  'gateway_subscription_id' => 'sub_1RboMKDrMO37mudt2R8vnj7o',
).
2025-6-19 14:42:53 - Updating payment #134078 with new data: array (
  'transaction_id' => 'sub_1RboMKDrMO37mudt2R8vnj7o',
)
2025-6-19 14:43:06 - "Can cancel" status for membership #34492: true.
2025-6-19 14:43:06 - "Can cancel" status for membership #34492: true.
2025-6-19 14:43:14 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:15 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:43:15 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:15 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:43:15 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:22 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 14:43:22 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:22 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 14:43:22 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:35 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 14:43:35 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:35 - Started new registration for membership level #11 via stripe.
2025-6-19 14:43:36 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 14:43:36 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:36 - Started new registration for membership level #11 via stripe.
2025-6-19 14:43:36 - Registration type: downgrade.
2025-6-19 14:43:36 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 27.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-19 14:43:36',
  'expiration_date' => '2026-06-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '4e9aaa5e274a77acd6c3bffb49126d5c',
  'upgraded_from' => '34492',
)
2025-6-19 14:43:37 - New payment inserted. ID: 134079; User ID: 25843; Amount: 27.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-19 14:43:37 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34493
2025-6-19 14:43:38 - Updating membership #34493. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 14:43:40 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 14:43:40 - Using recovered payment #134079 for registration. Transaction type: downgrade.
2025-6-19 14:43:40 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:40 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34493
2025-6-19 14:43:42 - Updating payment #134079 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RboN4DrMO37mudt2GDanV4U',
  'status' => 'complete',
)
2025-6-19 14:43:42 - Completing registration for customer #25781 via payment #134079.
2025-6-19 14:43:42 - Activating membership #34493.
2025-6-19 14:43:42 - Disabling all memberships for customer #25781 except: '34493'.
2025-6-19 14:43:42 - Disabling membership #34492.
2025-6-19 14:43:42 - Updating membership #34492. New data: array (
  'disabled' => 1,
).
2025-6-19 14:43:42 - "Can cancel" status for membership #34492: true.
2025-6-19 14:43:42 - "Can cancel" status for membership #34492: true.
2025-6-19 14:43:43 - Failed to cancel Stripe payment profile sub_1RboMKDrMO37mudt2R8vnj7o. Error code: resource_missing; Error Message: No such subscription: 'sub_1RboMKDrMO37mudt2R8vnj7o'.
2025-6-19 14:43:43 - Updating membership #34492. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-19 14:43:43',
).
2025-6-19 14:43:43 - Updating recurring status for membership #34492. Customer ID: 25781; Previous: true; New: false
2025-6-19 14:43:43 - Updating membership #34492. New data: array (
  'auto_renew' => 0,
).
2025-6-19 14:43:43 - Payment profile successfully cancelled for membership #34492.
2025-6-19 14:43:43 - Updating membership #34493. New data: array (
  'activated_date' => '2025-06-19 14:43:43',
).
2025-6-19 14:43:43 - Updating membership #34493. New data: array (
  'status' => 'active',
).
2025-6-19 14:43:43 - Removing old role subscriber, adding new role subscriber for membership #34493 (user ID #25843).
2025-6-19 14:43:43 - Active email sent to user #25843 for membership #34493.
2025-6-19 14:43:43 - Active email sent to admin(s) regarding membership #34493.
2025-6-19 14:43:43 - Payment Received email not sent to user #25843 for payment ID #134079 - message is empty or disabled.
2025-6-19 14:43:43 - Payment Received email not sent to admin(s) for payment ID #134079 - message is empty or disabled.
2025-6-19 14:43:43 - Updating membership #34493. New data: array (
  'times_billed' => 1,
).
2025-6-19 14:43:43 - Payment #134079 completed for member #25843 via Stripe gateway.
2025-6-19 14:43:45 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-19 23:59:59
2025-6-19 14:43:45 - Stripe Gateway: Creating subscription with 1781912625 start date via trial_end.
2025-6-19 14:43:46 - Updating membership #34493. New data: array (
  'gateway_subscription_id' => 'sub_1RboNBDrMO37mudtwHauEPvV',
).
2025-6-19 14:44:07 - "Can cancel" status for membership #34493: true.
2025-6-19 14:44:07 - "Can cancel" status for membership #34493: true.
2025-6-19 14:44:11 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:13 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:44:13 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:13 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:44:13 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:38 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-19 14:44:38 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:38 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-19 14:44:38 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:44 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-19 14:44:44 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:44 - Started new registration for membership level #7 via stripe.
2025-6-19 14:44:45 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-19 14:44:45 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:45 - Started new registration for membership level #7 via stripe.
2025-6-19 14:44:45 - Registration type: upgrade.
2025-6-19 14:44:45 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-19 14:44:45',
  'expiration_date' => '2026-01-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '7ea9a2d8dc6750a5a039881daa932ba7',
  'upgraded_from' => '34493',
)
2025-6-19 14:44:46 - New payment inserted. ID: 134080; User ID: 25843; Amount: 0.00; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-19 14:44:46 - Registration for user #25843 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2026-01-19 14:44:46; Membership ID: 34494
2025-6-19 14:44:46 - Updating membership #34494. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 14:44:48 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-19 14:44:48 - Using recovered payment #134080 for registration. Transaction type: upgrade.
2025-6-19 14:44:48 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:48 - Registration for user #25843 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2026-01-19 14:44:48; Membership ID: 34494
2025-6-19 14:44:50 - Updating payment #134080 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-19 14:44:50 - Completing registration for customer #25781 via payment #134080.
2025-6-19 14:44:50 - Activating membership #34494.
2025-6-19 14:44:50 - Disabling all memberships for customer #25781 except: '34494'.
2025-6-19 14:44:50 - Disabling membership #34493.
2025-6-19 14:44:50 - Updating membership #34493. New data: array (
  'disabled' => 1,
).
2025-6-19 14:44:50 - "Can cancel" status for membership #34493: true.
2025-6-19 14:44:50 - "Can cancel" status for membership #34493: true.
2025-6-19 14:44:52 - Failed to cancel Stripe payment profile sub_1RboNBDrMO37mudtwHauEPvV. Error code: resource_missing; Error Message: No such subscription: 'sub_1RboNBDrMO37mudtwHauEPvV'.
2025-6-19 14:44:52 - Updating membership #34493. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-19 14:44:52',
).
2025-6-19 14:44:52 - Updating recurring status for membership #34493. Customer ID: 25781; Previous: true; New: false
2025-6-19 14:44:52 - Updating membership #34493. New data: array (
  'auto_renew' => 0,
).
2025-6-19 14:44:52 - Payment profile successfully cancelled for membership #34493.
2025-6-19 14:44:52 - Updating membership #34494. New data: array (
  'activated_date' => '2025-06-19 14:44:52',
).
2025-6-19 14:44:52 - Updating membership #34494. New data: array (
  'status' => 'active',
).
2025-6-19 14:44:52 - Removing old role subscriber, adding new role subscriber for membership #34494 (user ID #25843).
2025-6-19 14:44:52 - Active email sent to user #25843 for membership #34494.
2025-6-19 14:44:52 - Active email sent to admin(s) regarding membership #34494.
2025-6-19 14:44:52 - Payment Received email not sent to user #25843 - payment amount is 0.
2025-6-19 14:44:52 - Updating membership #34494. New data: array (
  'times_billed' => 1,
).
2025-6-19 14:44:52 - Payment #134080 completed for member #25843 via Stripe gateway.
2025-6-19 14:44:53 - Stripe Gateway: Using subscription start date for subscription: 2026-01-19 14:44:48
2025-6-19 14:44:53 - Stripe Gateway: Creating subscription with 1768833888 start date via trial_end.
2025-6-19 14:44:55 - Updating membership #34494. New data: array (
  'gateway_subscription_id' => 'sub_1RboOIDrMO37mudtTlVI7TSK',
).
2025-6-19 14:44:55 - Updating payment #134080 with new data: array (
  'transaction_id' => 'sub_1RboOIDrMO37mudtTlVI7TSK',
)
2025-6-19 14:46:11 - "Can cancel" status for membership #34494: true.
2025-6-19 14:46:11 - "Can cancel" status for membership #34494: true.
2025-6-19 14:46:40 - "Can cancel" status for membership #34494: true.
2025-6-19 14:46:40 - "Can cancel" status for membership #34494: true.
2025-6-20 00:18:29 - Starting rcp_check_member_counts() cron job.
2025-6-20 00:18:29 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-20 00:18:29 - Updating payment #134061 with new data: array (
  'status' => 'abandoned',
)
2025-6-20 00:18:29 - Updating payment #134060 with new data: array (
  'status' => 'abandoned',
)
2025-6-20 00:18:29 - Updating payment #134059 with new data: array (
  'status' => 'abandoned',
)
