# Dynasty Trade Calculator - Product Requirements Document (PRD)

## Current Status (Updated December 2024)

### 🔧 Recently Completed
- **Debug Utility System**: Implemented comprehensive conditional logging system
  - `DTC_DEBUG` constant for basic debug control
  - `DTC_DEBUG_VERBOSE` constant for detailed object logging
  - Replaced all `error_log()` calls in Membership.php with conditional Debug methods
  - Created documentation and test utilities for debug configuration
- **Missing Shortcodes**: Recovered and added the missing shortcodes from the deleted `includes/membership.php`:
  - `dtc_register_form` - Displays membership registration form based on user eligibility
  - `dtc-easy-pricing-table` - Shows appropriate pricing table based on user status and pilot access
- **Complete Functionality**: All code from the old membership file has now been migrated to the new class

### ✅ Completed
- **Composer Autoload Migration**: Successfully migrated membership functionality from function-based to class-based approach
- **Plugin Class**: Created centralized `Plugin` class for initialization
- **Membership Class**: Consolidated all membership functionality into `src/Membership.php`
- **File Structure**: Organized code with proper PSR-4 namespace (`DynastyTradeCalculator\`)
- **WordPress Hooks**: Integrated hooks directly into the Membership class
- **Shortcodes**: Added missing shortcodes (`dtc_register_form`, `dtc-easy-pricing-table`)
- **Code Updates**: Updated `user/calculator.php` to use new class methods
- **Complete Migration**: All functionality from old `includes/membership.php` has been migrated

### 🔄 Current Architecture

```
src/
├── Plugin.php          # Main plugin initialization class
├── Membership.php      # Complete membership functionality + hooks
├── Debug.php           # Conditional logging utility (NEW)
└── RestApi.php         # REST API endpoints

wp-content/plugins/dynasty-trade-calculator/
├── dynasty-trade-calculator.php  # Main plugin file (minimal)
├── user/calculator.php           # Updated to use new classes
├── tests/                        # Comprehensive test suite (56 tests, 100% passing)
├── docs/                         # Documentation and guides
│   ├── PRD.md                   # This document
│   ├── debug-configuration.md   # Debug setup guide
│   ├── test-analysis-2024.md    # Test coverage analysis
│   └── wp-config-example.php    # Configuration examples
└── test-debug.php               # Debug utility test script (remove in production)
```

### 🎯 Next Steps (TODO)

#### Phase 1: Complete Migration
1. **Update All Files**: Find and update remaining files that use old membership functions
   - Search for: `dtc_get_current_user_customer`, `dtc_get_current_user_membership`, etc.
   - Replace with: `Membership::getCurrentUserCustomer()`, `Membership::getCurrentUserMembership()`, etc.
   - Add `use DynastyTradeCalculator\Membership;` to files that need it

2. **Test Migration**: Run existing tests to ensure functionality still works
   - Execute: `composer test` or `vendor/bin/phpunit`
   - Fix any broken tests
   - Verify membership functionality works in browser

#### Phase 2: Expand Class-Based Approach
3. **Calculator Class**: Convert calculator functionality to class-based
   - Create `src/Calculator.php`
   - Move calculator logic from `user/calculator.php`
   - Update hooks and AJAX handlers

4. **API Class**: Convert REST API functionality to class-based
   - Create `src/Api.php`
   - Move API logic from `includes/rest-api.php`
   - Update route registrations

5. **Admin Class**: Convert admin functionality to class-based
   - Create `src/Admin.php`
   - Move admin logic from `admin/` files
   - Update admin hooks and pages

#### Phase 3: Clean Up
6. **Remove Old Files**: Delete old function-based files after migration
   - Remove `includes/functions.php` (if exists)
   - Remove other old include files
   - Clean up unused directories

7. **Update Documentation**: Update all documentation to reflect new structure
   - Update README.md
   - Update code comments
   - Create developer documentation

## Migration Guidelines

### Class Naming Convention
- **File name = Class name**: `Membership.php` contains `class Membership`
- **Namespace**: All classes use `DynastyTradeCalculator\` namespace
- **Methods**: Use camelCase for method names (e.g., `getCurrentUserCustomer()`)

### Function to Class Method Mapping
```php
// OLD (function-based)
dtc_get_current_user_customer() 
dtc_get_current_user_membership()
dtc_is_current_user_invited_to_chatdtc_pilot()

// NEW (class-based)
Membership::getCurrentUserCustomer()
Membership::getCurrentUserMembership()
Membership::isCurrentUserInvitedToChatdtcPilot()
```

### File Updates Required
1. **Add use statement** at top of file:
   ```php
   use DynastyTradeCalculator\Membership;
   ```

2. **Replace function calls** with class methods:
   ```php
   // OLD
   $customer = dtc_get_current_user_customer();
   
   // NEW
   $customer = Membership::getCurrentUserCustomer();
   ```

## Testing Strategy

### Current Test Status ✅
- **67 tests, 283 assertions - ALL PASSING (100% success rate)**
- **Execution time:** 0.033 seconds (very fast)
- **Coverage:** ~90% of core subscription logic
- Tests are located in `tests/` directory and focus on logic validation

### Test Files Overview
| File | Purpose | Tests | Status |
|------|---------|-------|--------|
| `BasicTest.php` | Basic PHP functionality | 5 | ✅ |
| `MembershipTest.php` | Core membership logic | 15 | ✅ |
| `MembershipMockTest.php` | Mock object testing | 18 | ✅ |
| `MembershipApiTest.php` | API integration | 12 | ✅ |
| `MembershipEdgeCasesTest.php` | Edge cases | 6 | ✅ |
| `DebugTest.php` | Debug utility testing | 11 | ✅ |

### Well-Tested Areas ✅
- RotoGPT API integration (signin, create, update, cancel)
- Membership level mapping and subscription types
- Upgrade/downgrade detection logic
- Pilot program invitation handling
- Date handling and expiration logic
- API request/response structure validation
- Edge cases and boundary conditions
- **Debug utility functionality** (comprehensive testing added)
- Conditional logging and configuration
- Mock object testing for WordPress integration

### Areas for Future Enhancement 🔍
- WordPress hooks and actions integration testing
- API failure scenarios and error recovery
- Performance testing for high-traffic scenarios
- Load testing for concurrent subscription operations

## Benefits Achieved

1. **Better Organization**: Related functionality grouped in classes
2. **Namespace Protection**: Avoids function name conflicts
3. **Autoloading**: No manual include/require statements
4. **IDE Support**: Better autocomplete and type hints
5. **Maintainability**: Easier to extend and modify
6. **Modern PHP**: Following current best practices

## Risk Mitigation

### Backward Compatibility
- **No backward compatibility layer**: Clean migration approach
- **All old function calls must be updated**: Prevents mixed approaches
- **Comprehensive testing required**: Ensure nothing breaks

### Rollback Plan
- Git version control allows easy rollback if needed
- Keep old files temporarily until migration is confirmed working
- Test thoroughly in development before production deployment

## Success Criteria

### Phase 1 Complete When:
- [ ] All files updated to use new class methods
- [ ] All tests passing
- [ ] No PHP errors in browser
- [ ] Membership functionality works correctly

### Full Migration Complete When:
- [ ] All functionality converted to class-based approach
- [ ] Old files removed
- [ ] Documentation updated
- [ ] Performance maintained or improved
- [ ] Code is cleaner and more maintainable

## Notes

- **No backward compatibility**: Clean break from old approach
- **One class per file**: Following PSR-4 standards
- **Minimal main plugin file**: Keep initialization simple
- **Comprehensive testing**: Essential due to no backward compatibility
- **Gradual migration**: One functionality area at a time
