<?php
/**
 * Dynasty Trade Calculator - Membership Class
 *
 * Handles all membership-related functionality including:
 * - Customer and membership retrieval
 * - RotoGPT integration
 * - Membership status transitions
 * - WordPress hooks and actions
 */

namespace DynastyTradeCalculator;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Membership
{
    /**
     * Initialize membership functionality and hooks
     */
    public static function init()
    {
        // Register WordPress hooks
        add_action('rcp_transition_membership_status', [self::class, 'handleMembershipStatusTransition'], 10, 3);
        add_action('rcp_new_membership_added', [self::class, 'handleNewMembershipAdded'], 10, 1);

        // Register shortcodes
        add_shortcode('dtc_register_form', [self::class, 'registerFormShortcode']);
        add_shortcode('dtc-easy-pricing-table', [self::class, 'easyPricingTableShortcode']);
    }
    /**
     * Get the current user's RCP customer object
     * 
     * @return \RCP_Customer|null Customer object or null if not found
     */
    public static function getCurrentUserCustomer()
    {
        $current_user = wp_get_current_user();
        $current_user_id = $current_user->ID;
        $current_customer = rcp_get_customer_by_user_id($current_user_id);

        return $current_customer;
    }

    /**
     * Get the current user's active or cancelled membership
     * 
     * @return \RCP_Membership|null Membership object or null if not found/expired
     */
    public static function getCurrentUserMembership()
    {
        $current_customer = self::getCurrentUserCustomer();
        $current_membership = $current_customer ? ($current_customer->get_memberships(['status' => ['active', 'cancelled']])[0] ?? null) : null;

        // If membership is cancelled, check if it's still valid
        if ($current_membership && $current_membership->get_status() === 'cancelled') {
            $expiration_date = strtotime($current_membership->get_expiration_date(false));
            if ($expiration_date < time()) {
                return null;
            }
        }

        return $current_membership;
    }

    /**
     * Get the current user's membership that was active at a specific date
     * 
     * @param string $date Date to check membership for
     * @return \RCP_Membership|null Membership object or null if not found
     */
    public static function getCurrentUserMembershipAtDate($date)
    {
        $current_customer = self::getCurrentUserCustomer();
        if (empty($current_customer)) {
            return [];
        }

        $memberships = $current_customer->get_memberships();
        if (empty($memberships)) {
            return [];
        }
        foreach ($memberships as $membership) {
            if (
                $membership->get_activated_date()
                && $membership->get_expiration_date(false)
                && $membership->get_activated_date() <= $date 
                && $membership->get_expiration_date(false) >= $date
            ) {
                return $membership;
            }
        }
        
        return null;
    }

    /**
     * Check if a customer is invited to the ChatDTC pilot program
     * 
     * @param \RCP_Customer $customer Customer object to check
     * @return bool True if invited, false otherwise
     */
    public static function isCustomerInvitedToChatdtcPilot($customer)
    {
        $notes = $customer->get_notes();
        $is_invited = strpos($notes, DTC_CHATDTC_PILOT_INVITATION_TEXT) !== false;
        if (!$is_invited) {
            $membership = $customer->get_memberships(['status' => 'active'])[0] ?? null;
            if (
                $membership
                && (
                    $membership->get_recurring_amount() == 2.99
                    || $membership->get_recurring_amount() == 29.99
                )
            ) {
                $is_invited = true;
            }
        }

        return $is_invited;
    }

    /**
     * Check if a customer has lost legacy membership options
     * 
     * @param \RCP_Customer $customer Customer object to check
     * @return bool True if legacy options lost, false otherwise
     */
    public static function isLegacyMembershipOptionsLost($customer)
    {
        $notes = $customer->get_notes();
        $is_legacy_lost = strpos($notes, DTC_LEGACY_MEMBERSHIP_LOST_NOTE) !== false;

        return $is_legacy_lost;
    }

    /**
     * Check if the current user is invited to the ChatDTC pilot program
     * 
     * @return bool True if invited, false otherwise
     */
    public static function isCurrentUserInvitedToChatdtcPilot()
    {
        $current_customer = self::getCurrentUserCustomer();
        if (empty($current_customer)) {
            return false;
        }

        return self::isCustomerInvitedToChatdtcPilot($current_customer);
    }

    /**
     * Get the RotoGPT subscription type for a membership
     * 
     * @param \RCP_Membership $membership Membership object
     * @return string|null RotoGPT subscription type or null if not found
     */
    public static function getRotoGptSubscription($membership)
    {
        $rotogpt_subscription = null;
        $is_customer_invited_to_pilot = self::isCustomerInvitedToChatdtcPilot($membership->get_customer());
        $membership_level = $membership->get_object_id();
        error_log('DTC RotoGPT: Membership level in getRotoGptSubscription: ' . $membership_level);
        error_log('DTC RotoGPT: DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE: ' . print_r(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, true));
        foreach (DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE as $mapping) {
            if ($mapping['membership_level_id'] == $membership_level) {
                error_log('DTC RotoGPT: Found mapping for membership level ' . $membership_level . ': ' . print_r($mapping, true));
                $rotogpt_subscription = $mapping['rotogpt_subscription_type'];
                break;
            }
        }

        if (empty($rotogpt_subscription) && $is_customer_invited_to_pilot && DTC_IS_CHATDTC_PILOT_ACTIVE) {
            $rotogpt_subscription = DTC_ROTOGPT_TEST_DRIVE_SUBSCRIPTION;
        }

        if (empty($rotogpt_subscription) && !DTC_IS_CHATDTC_PILOT_ACTIVE) {
            $rotogpt_subscription = DTC_ROTOGPT_TEST_DRIVE_SUBSCRIPTION;
        }

        return $rotogpt_subscription;
    }

    /**
     * Sign in to RotoGPT and get access token
     * 
     * @param \RCP_Membership $membership Membership object
     * @param string $rotogpt_subscription RotoGPT subscription type
     * @return string|false Access token or false on failure
     */
    public static function rotoGptSignin($membership, $rotogpt_subscription)
    {
        $rotogpt_signin_request_body = json_encode([
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'client_password' => DTC_ROTOGPT_PASSWORD,
            'current_user' => [
                'user_id' => (string) $membership->get_customer()->get_id(),
                'membership' => $rotogpt_subscription,
                'sign_up_date' => date('d-m-Y H:i:s', strtotime($membership->get_activated_date())),
            ],
        ]);
        error_log('DTC RotoGPT Signin: Request body: ' . print_r($rotogpt_signin_request_body, 1));

        $api_endpoint = DTC_IS_PRODUCTION ? 'https://api.rotogpt.com/signin' : 'https://api.dev.rotogpt.com/signin';

        $rotogpt_signin_response = wp_remote_post(
            $api_endpoint,
            [
                'body' => $rotogpt_signin_request_body,
                'timeout' => 30,
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]
        );

        error_log('DTC RotoGPT Signin: Sending request to ' . $api_endpoint);
        error_log('DTC RotoGPT Signin: Request body: ' . print_r($rotogpt_signin_response, 1));

        // Check for HTTP errors
        if (is_wp_error($rotogpt_signin_response)) {
            error_log('DTC RotoGPT Signin Error: HTTP request failed - ' . $rotogpt_signin_response->get_error_message());
            return false;
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($rotogpt_signin_response);
        if ($response_code !== 200) {
            error_log('DTC RotoGPT Signin Error: HTTP ' . $response_code);
            error_log('DTC RotoGPT Signin Response: ' . wp_remote_retrieve_body($rotogpt_signin_response));
            return false;
        }

        $rotogpt_signin_response_body = wp_remote_retrieve_body($rotogpt_signin_response);
        $rotogpt_signin_response = json_decode($rotogpt_signin_response_body, true);

        error_log('DTC RotoGPT Signin: Response body: ' . print_r($rotogpt_signin_response, 1));

        return $rotogpt_signin_response['accessToken'];
    }

    /**
     * Update an existing RotoGPT subscription for upgrades/downgrades
     *
     * @param \RCP_Membership $membership New membership object
     * @param \RCP_Membership $old_membership Old membership object
     * @param string $rotogpt_subscription_type New RotoGPT subscription type
     * @param string $old_rotogpt_subscription Old RotoGPT subscription type
     * @param string $new_rotogpt_subscription New RotoGPT subscription type
     * @return bool Success status
     */
    public static function rotoGptUpdateSubscription($membership, $old_membership, $rotogpt_subscription_type, $old_rotogpt_subscription, $new_rotogpt_subscription) {
        $membership_id = $membership->get_id();

        // Determine if this is an upgrade or downgrade and when to apply the change
        // instead of pricing we need to follow the DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE order
        // e.x. low to high: free, standard_50, standard_100, standard_200, vip, admin
        $is_upgrade = false;
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
        $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
        if ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index) {
            $is_upgrade = true;
        }

        $user_id = $membership->get_customer()->get_id();
        $request = [
            'user_id' => (string) $user_id,
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'subscription_type' => $rotogpt_subscription_type,
        ];

        if ($is_upgrade) {
            // For upgrades, apply immediately
            $request['apply_immediately'] = true;
        } else {
            // For downgrades, apply at the end of the current subscription period
            $request['apply_immediately'] = false;

            // Get the expiration date of the old membership
            $old_expiration = $old_membership->get_expiration_date();
            if (!empty($old_expiration) && $old_expiration !== '0000-00-00 00:00:00') {
                // Format the date for RotoGPT API (assuming they expect ISO format)
                $start_date = date('Y-m-d\TH:i:s\Z', strtotime($old_expiration));
                $request['new_subscription_start_date'] = $start_date;
            } else {
                // If no expiration date, apply immediately as fallback
                $request['apply_immediately'] = true;
                unset($request['new_subscription_start_date']);
            }
        }

        // Use the new membership for signin (it has the current user info)
        $access_token = self::rotoGptSignin($membership, $new_rotogpt_subscription ?: $old_rotogpt_subscription);

        if (!$access_token) {
            error_log('DTC RotoGPT Update Error: Failed to get access token for membership #' . $membership_id);
            return false;
        }

        $api_endpoint = DTC_IS_PRODUCTION
            ? 'https://api.rotogpt.com/subscriptions/update'
            : 'https://api.dev.rotogpt.com/subscriptions/update';

        error_log('DTC RotoGPT Update: Sending request to ' . $api_endpoint);
        error_log('DTC RotoGPT Update: Request body: ' . json_encode($request));

        $response = wp_remote_post(
            $api_endpoint,
            [
                'body' => json_encode($request),
                'timeout' => 30,
                'headers' => [
                    'Authorization' => "Bearer {$access_token}",
                    'Content-Type' => 'application/json',
                ],
            ]
        );

        // Check for HTTP errors
        if (is_wp_error($response)) {
            error_log('DTC RotoGPT Update Error: HTTP request failed - ' . $response->get_error_message());
            return false;
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            error_log('DTC RotoGPT Update Error: HTTP ' . $response_code);
            error_log('DTC RotoGPT Update Response: ' . wp_remote_retrieve_body($response));
            return false;
        }

        error_log('DTC RotoGPT Update: Response code: ' . $response_code);
        error_log('DTC RotoGPT Update: Response body: ' . wp_remote_retrieve_body($response));

        // Add note to customer about the RotoGPT subscription update
        $old_level_name = $old_membership->get_membership_level_name();
        $new_level_name = $membership->get_membership_level_name();

        if ($is_upgrade) {
            $note = sprintf(
                'RotoGPT subscription upgraded from %s (%s) to %s (%s) - applied immediately',
                $old_level_name,
                $old_rotogpt_subscription ?: 'none',
                $new_level_name,
                $rotogpt_subscription_type
            );
        } else {
            $schedule_info = isset($request['new_subscription_start_date'])
                ? ' - scheduled for ' . $request['new_subscription_start_date']
                : ' - applied immediately (no expiration date found)';
            $note = sprintf(
                'RotoGPT subscription downgraded from %s (%s) to %s (%s)%s',
                $old_level_name,
                $old_rotogpt_subscription ?: 'none',
                $new_level_name,
                $rotogpt_subscription_type,
                $schedule_info
            );
        }

        $membership->get_customer()->add_note($note);

        $log_message = $is_upgrade
            ? 'DTC RotoGPT Upgrade Success: Updated user #' . $user_id . ' from ' . ($old_rotogpt_subscription ?: 'none') . ' to ' . $rotogpt_subscription_type . ' (immediate)'
            : 'DTC RotoGPT Downgrade Success: Scheduled user #' . $user_id . ' from ' . ($old_rotogpt_subscription ?: 'none') . ' to ' . $rotogpt_subscription_type . (isset($request['new_subscription_start_date']) ? ' starting ' . $request['new_subscription_start_date'] : ' (immediate - no expiration)');

        error_log($log_message);
        return true;
    }

    /**
     * Create a new RotoGPT subscription for a new user
     *
     * @param \RCP_Membership $membership Membership object
     * @param string $rotogpt_subscription_type RotoGPT subscription type
     * @return bool Success status
     */
    public static function rotoGptCreateSubscription($membership, $rotogpt_subscription_type) {
        $user_id = $membership->get_customer()->get_id();
        error_log('DTC RotoGPT Create: Creating subscription for user # (customer id)' . $user_id . ' with type ' . $rotogpt_subscription_type);

        $request = [
            'user_id' => (string) $user_id,
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'subscription_type' => $rotogpt_subscription_type,
        ];

        $api_endpoint = DTC_IS_PRODUCTION
            ? 'https://api.rotogpt.com/subscriptions/create'
            : 'https://api.dev.rotogpt.com/subscriptions/create';

        error_log('DTC RotoGPT Create: Sending request to ' . $api_endpoint);
        error_log('DTC RotoGPT Create: Request body: ' . json_encode($request));

        $response = wp_remote_post(
            $api_endpoint,
            [
                'body' => json_encode($request),
                'timeout' => 30,
                'headers' => [
                    // 'Authorization' => "Bearer {$access_token}", // not needed
                    'Content-Type' => 'application/json',
                ],
            ]
        );

        error_log('DTC RotoGPT Create: Request body: ' . print_r($response, 1));

        // Check for HTTP errors
        if (is_wp_error($response)) {
            error_log('DTC RotoGPT Create Error: HTTP request failed - ' . $response->get_error_message());
            return false;
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            error_log('DTC RotoGPT Create Error: HTTP ' . $response_code);
            error_log('DTC RotoGPT Create Response: ' . wp_remote_retrieve_body($response));
            return false;
        }

        error_log('DTC RotoGPT Create: Response code: ' . $response_code);
        error_log('DTC RotoGPT Create: Response body: ' . wp_remote_retrieve_body($response));

        // Add note to customer about the RotoGPT subscription creation
        $level_name = $membership->get_membership_level_name();
        $note = sprintf(
            'RotoGPT subscription created: %s (%s)',
            $level_name,
            $rotogpt_subscription_type
        );
        $membership->get_customer()->add_note($note);

        error_log('DTC RotoGPT Create Success: Created subscription for user #' . $user_id . ' with type ' . $rotogpt_subscription_type);
        return true;
    }

    /**
     * Handle membership status transitions - specifically when memberships expire
     * This function cancels the RotoGPT account when a membership expires
     */
    public static function handleMembershipStatusTransition($old_status, $new_status, $membership_id)
    {
        if ($new_status != 'expired') {
            return;
        }

        $membership = rcp_get_membership($membership_id);
        $customer = $membership->get_customer();
        $customer->add_note(DTC_LEGACY_MEMBERSHIP_LOST_NOTE);

        $rogogpt_subscription = self::getRotoGptSubscription($membership);
        if (empty($rogogpt_subscription)) {
            return;
        }
        $access_token = self::rotoGptSignin($membership, $rogogpt_subscription);

        $user_id = $membership->get_customer()->get_id();
        $request = [
            'user_id' => (string) $user_id,
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'subscription_type' => 'cancelled_account',
            'apply_immediately' => true,
        ];
        $response = wp_remote_post(
            DTC_IS_PRODUCTION
                ? 'https://api.rotogpt.com//subscriptions/update'
                : 'https://api.dev.rotogpt.com/subscriptions/update',
                // : 'https://api.dev.rotogpt.com/cancel_account', // old enddpoint
            [
                'body' => json_encode($request),
                'timeout' => 30,
                'headers' => [
                    'Authorization' => "Bearer {$access_token}",
                    'Content-Type' => 'application/json',
                ],
            ]
        );
        $response_body = wp_remote_retrieve_body($response);
        $response = json_decode($response_body, true);
    }

    /**
     * Handle new memberships and membership upgrades/downgrades
     * This function creates or updates RotoGPT subscriptions when membership changes occur
     */
    public static function handleNewMembershipAdded($membership_id)
    {
        error_log('DTC RotoGPT: Processing membership #' . $membership_id);
        $membership = rcp_get_membership($membership_id);
        error_log('DTC RotoGPT: Membership level: ' . $membership->get_object_id());

        // get customer
        $customer = $membership->get_customer();
        error_log('DTC RotoGPT: Customer ID: ' . $customer->get_id());

        // Check if this is an upgrade/downgrade or a brand new membership
        $is_upgrade_downgrade = $membership->was_upgrade();

        if ($is_upgrade_downgrade) {
            error_log('DTC RotoGPT: Membership #' . $membership_id . ' is an upgrade/downgrade - using update endpoint');

            // Get the old membership that was upgraded/downgraded from
            $old_membership_id = $membership->get_upgraded_from();
            $old_membership = rcp_get_membership($old_membership_id);

            if (empty($old_membership)) {
                error_log('DTC RotoGPT Update Error: Could not find old membership #' . $old_membership_id);
                return;
            }

            // Get RotoGPT subscription types for both old and new memberships
            $old_rotogpt_subscription = self::getRotoGptSubscription($old_membership);
            $new_rotogpt_subscription = self::getRotoGptSubscription($membership);

            error_log('DTC RotoGPT: Old RotoGPT subscription: ' . ($old_rotogpt_subscription ?: 'none'));
            error_log('DTC RotoGPT: New RotoGPT subscription: ' . ($new_rotogpt_subscription ?: 'none'));

            // Only proceed if the new membership has a valid RotoGPT subscription
            if (empty($new_rotogpt_subscription)) {
                error_log('DTC RotoGPT Update Error: No RotoGPT subscription type found for new membership level');
                return;
            }

            // Call the update function
            $success = self::rotoGptUpdateSubscription(
                $membership,
                $old_membership,
                $new_rotogpt_subscription,
                $old_rotogpt_subscription,
                $new_rotogpt_subscription
            );

            if (!$success) {
                error_log('DTC RotoGPT Update Error: Failed to update RotoGPT subscription for membership #' . $membership_id);
            }

        } else {
            error_log('DTC RotoGPT: Membership #' . $membership_id . ' is a new membership - using create endpoint');

            // This is a brand new membership, create a new RotoGPT subscription
            $rotogpt_subscription_type = self::getRotoGptSubscription($membership);

            if (empty($rotogpt_subscription_type)) {
                error_log('DTC RotoGPT Create Error: No RotoGPT subscription type found for membership level');
                return;
            }

            // Call the create function
            $success = self::rotoGptCreateSubscription($membership, $rotogpt_subscription_type);

            if (!$success) {
                error_log('DTC RotoGPT Create Error: Failed to create RotoGPT subscription for membership #' . $membership_id);
            }
        }
    }

    /**
     * Registration form shortcode
     * Displays available membership levels based on user's current status and eligibility
     */
    public static function registerFormShortcode($atts = [])
    {
        $membership = self::getCurrentUserMembershipAtDate(DTC_LEGACY_MEMBERSHIP_REFERENCE_DATE);
        $is_legacy_lost = $membership ? self::isLegacyMembershipOptionsLost($membership->get_customer()) : true;
        $current_membership = self::getCurrentUserMembership();
        $membership_levels = [];

        foreach (DTC_MEMBERSHIP_LEVEL_RULES as $rule) {
            $membership_level = $GLOBALS['rcp_levels_db']->get_level($rule['membership_level_id']);
            if ($current_membership && $current_membership->get_recurring_amount() == $membership_level->price) {
                continue;
            }
            if (
                $rule['valid_for_users_that_paid_any_of_these_amounts_only']
                && (
                    empty($membership)
                    || $is_legacy_lost
                    || !in_array($membership->get_recurring_amount(), $rule['valid_for_users_that_paid_any_of_these_amounts_only'])
                )
            ) {
                continue;
            }
            if (
                $rule['is_chatdtc_membership']
                && DTC_IS_CHATDTC_PILOT_ACTIVE
                && !self::isCurrentUserInvitedToChatdtcPilot()
            ) {
                continue;
            }
            $membership_levels[] = $rule['membership_level_id'];
        }

        return do_shortcode('[register_form ids="' . implode(',', $membership_levels) . '"]');
    }

    /**
     * Easy pricing table shortcode
     * Displays the appropriate pricing table based on user's status and pilot eligibility
     */
    public static function easyPricingTableShortcode($atts = [])
    {
        $membership = self::getCurrentUserMembershipAtDate(DTC_LEGACY_MEMBERSHIP_REFERENCE_DATE);
        $is_legacy_lost = $membership ? self::isLegacyMembershipOptionsLost($membership->get_customer()) : true;
        $is_current_user_invited = self::isCurrentUserInvitedToChatdtcPilot();

        if ($is_legacy_lost) {
            if ($is_current_user_invited && DTC_IS_CHATDTC_PILOT_ACTIVE) {
                return do_shortcode('[easy-pricing-table id="1234"]'); // ChatDTC pilot table
            } else {
                return do_shortcode('[easy-pricing-table id="1235"]'); // Standard new user table
            }
        } else {
            if ($is_current_user_invited && DTC_IS_CHATDTC_PILOT_ACTIVE) {
                return do_shortcode('[easy-pricing-table id="1236"]'); // Legacy user with pilot access
            } else {
                return do_shortcode('[easy-pricing-table id="1237"]'); // Legacy user standard table
            }
        }
    }
}
